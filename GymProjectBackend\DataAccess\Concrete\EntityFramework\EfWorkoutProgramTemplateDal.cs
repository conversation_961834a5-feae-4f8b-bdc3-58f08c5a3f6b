using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramTemplateDal : EfCompanyEntityRepositoryBase<WorkoutProgramTemplate, GymContext>, IWorkoutProgramTemplateDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfWorkoutProgramTemplateDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfWorkoutProgramTemplateDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList()
        {
            int companyId = _companyContext.GetCompanyId();

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from wpt in _context.WorkoutProgramTemplates
                             where wpt.CompanyID == companyId && wpt.IsActive == true
                             select new WorkoutProgramTemplateListDto
                             {
                                 WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 Description = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 IsActive = wpt.IsActive,
                                 CreationDate = wpt.CreationDate,
                                 DayCount = _context.WorkoutProgramDays
                                     .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                     .Count(),
                                 ExerciseCount = _context.WorkoutProgramExercises
                                         .Where(e => _context.WorkoutProgramDays
                                             .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                             .Select(d => d.WorkoutProgramDayID)
                                             .Contains(e.WorkoutProgramDayID))
                                         .Count()
                                 };

                return result.OrderByDescending(x => x.CreationDate).ToList();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from wpt in context.WorkoutProgramTemplates
                                 where wpt.CompanyID == companyId && wpt.IsActive == true
                                 select new WorkoutProgramTemplateListDto
                                 {
                                     WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                                     ProgramName = wpt.ProgramName,
                                     Description = wpt.Description,
                                     ExperienceLevel = wpt.ExperienceLevel,
                                     TargetGoal = wpt.TargetGoal,
                                     IsActive = wpt.IsActive,
                                     CreationDate = wpt.CreationDate,
                                     DayCount = context.WorkoutProgramDays
                                         .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                         .Count(),
                                     ExerciseCount = context.WorkoutProgramExercises
                                             .Where(e => context.WorkoutProgramDays
                                                 .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                                 .Select(d => d.WorkoutProgramDayID)
                                                 .Contains(e.WorkoutProgramDayID))
                                             .Count()
                                     };

                    return result.OrderByDescending(x => x.CreationDate).ToList();
                }
            }
        }

        public WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId)
        {
            int companyId = _companyContext.GetCompanyId();

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var template = _context.WorkoutProgramTemplates
                    .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                    .Select(wpt => new WorkoutProgramTemplateDto
                    {
                        WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                        CompanyID = wpt.CompanyID,
                        ProgramName = wpt.ProgramName,
                        Description = wpt.Description,
                        ExperienceLevel = wpt.ExperienceLevel,
                        TargetGoal = wpt.TargetGoal,
                        IsActive = wpt.IsActive,
                        CreationDate = wpt.CreationDate,
                        DayCount = _context.WorkoutProgramDays
                            .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                            .Count()
                    })
                    .FirstOrDefault();

                if (template != null)
                {
                    // Günleri getir
                    template.Days = GetWorkoutProgramDays(_context, templateId);
                }

                return template;
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var template = context.WorkoutProgramTemplates
                        .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                        .Select(wpt => new WorkoutProgramTemplateDto
                        {
                            WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                            CompanyID = wpt.CompanyID,
                            ProgramName = wpt.ProgramName,
                            Description = wpt.Description,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            IsActive = wpt.IsActive,
                            CreationDate = wpt.CreationDate,
                            DayCount = context.WorkoutProgramDays
                                .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                .Count()
                        })
                        .FirstOrDefault();

                    if (template != null)
                    {
                        // Günleri getir
                        template.Days = GetWorkoutProgramDays(context, templateId);
                    }

                    return template;
                }
            }
        }

        private List<WorkoutProgramDayDto> GetWorkoutProgramDays(GymContext context, int templateId)
        {
            var days = context.WorkoutProgramDays
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToList();

            // Her gün için egzersizleri getir
            foreach (var day in days)
            {
                day.Exercises = GetWorkoutProgramExercises(context, day.WorkoutProgramDayID);
            }

            return days;
        }

        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercises(GymContext context, int dayId)
        {
            var exercises = from wpe in context.WorkoutProgramExercises
                           where wpe.WorkoutProgramDayID == dayId
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           };

            var result = exercises.OrderBy(e => e.OrderIndex).ToList();

            // Egzersiz isimlerini getir
            foreach (var exercise in result)
            {
                if (exercise.ExerciseType == "System")
                {
                    var systemExercise = context.SystemExercises
                        .Where(se => se.SystemExerciseID == exercise.ExerciseID)
                        .Select(se => new { se.ExerciseName, se.Description, CategoryName = context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == se.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (systemExercise != null)
                    {
                        exercise.ExerciseName = systemExercise.ExerciseName;
                        exercise.ExerciseDescription = systemExercise.Description;
                        exercise.CategoryName = systemExercise.CategoryName;
                    }
                }
                else if (exercise.ExerciseType == "Company")
                {
                    var companyExercise = context.CompanyExercises
                        .Where(ce => ce.CompanyExerciseID == exercise.ExerciseID)
                        .Select(ce => new { ce.ExerciseName, ce.Description, CategoryName = context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == ce.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (companyExercise != null)
                    {
                        exercise.ExerciseName = companyExercise.ExerciseName;
                        exercise.ExerciseDescription = companyExercise.Description;
                        exercise.CategoryName = companyExercise.CategoryName;
                    }
                }
            }

            return result;
        }

        public bool CheckProgramNameExists(string programName, int? excludeId = null)
        {
            int companyId = _companyContext.GetCompanyId();

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var query = _context.WorkoutProgramTemplates
                    .Where(t => t.ProgramName == programName && t.IsActive == true && t.CompanyID == companyId);

                if (excludeId.HasValue)
                {
                    query = query.Where(t => t.WorkoutProgramTemplateID != excludeId.Value);
                }

                return query.Any();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var query = context.WorkoutProgramTemplates
                        .Where(t => t.ProgramName == programName && t.IsActive == true && t.CompanyID == companyId);

                    if (excludeId.HasValue)
                    {
                        query = query.Where(t => t.WorkoutProgramTemplateID != excludeId.Value);
                    }

                    return query.Any();
                }
            }
        }

        public void AddWorkoutProgramWithDaysAndExercises(WorkoutProgramTemplateAddDto templateAddDto, int companyId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                AddWorkoutProgramWithContext(_context, templateAddDto, companyId);
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    AddWorkoutProgramWithContext(context, templateAddDto, companyId);
                    context.SaveChanges();
                }
            }
        }

        private void AddWorkoutProgramWithContext(GymContext context, WorkoutProgramTemplateAddDto templateAddDto, int companyId)
        {
            // Ana program şablonunu oluştur
            var template = new WorkoutProgramTemplate
            {
                CompanyID = companyId,
                ProgramName = templateAddDto.ProgramName,
                Description = templateAddDto.Description,
                ExperienceLevel = templateAddDto.ExperienceLevel,
                TargetGoal = templateAddDto.TargetGoal,
                IsActive = true,
                CreationDate = System.DateTime.Now
            };

            context.WorkoutProgramTemplates.Add(template);
            context.SaveChanges(); // ID'yi almak için

            // Günleri ekle
            foreach (var dayDto in templateAddDto.Days)
            {
                var day = new WorkoutProgramDay
                {
                    WorkoutProgramTemplateID = template.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    DayNumber = dayDto.DayNumber,
                    DayName = dayDto.DayName,
                    IsRestDay = dayDto.IsRestDay,
                    CreationDate = System.DateTime.Now
                };

                context.WorkoutProgramDays.Add(day);
                context.SaveChanges(); // ID'yi almak için

                // Egzersizleri ekle (dinlenme günü değilse)
                if (!dayDto.IsRestDay)
                {
                    foreach (var exerciseDto in dayDto.Exercises)
                    {
                        var exercise = new WorkoutProgramExercise
                        {
                            WorkoutProgramDayID = day.WorkoutProgramDayID,
                            CompanyID = companyId,
                            ExerciseType = exerciseDto.ExerciseType,
                            ExerciseID = exerciseDto.ExerciseID,
                            OrderIndex = exerciseDto.OrderIndex,
                            Sets = exerciseDto.Sets,
                            Reps = exerciseDto.Reps,
                            RestTime = exerciseDto.RestTime,
                            Notes = exerciseDto.Notes,
                            CreationDate = System.DateTime.Now
                        };

                        context.WorkoutProgramExercises.Add(exercise);
                    }
                }
            }
        }

        public void UpdateWorkoutProgramWithDaysAndExercises(WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                UpdateWorkoutProgramWithContext(_context, templateUpdateDto, companyId);
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    UpdateWorkoutProgramWithContext(context, templateUpdateDto, companyId);
                    context.SaveChanges();
                }
            }
        }

        private void UpdateWorkoutProgramWithContext(GymContext context, WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId)
        {
            // Mevcut programı bul ve güncelle
            var existingTemplate = context.WorkoutProgramTemplates
                .FirstOrDefault(t => t.WorkoutProgramTemplateID == templateUpdateDto.WorkoutProgramTemplateID && t.CompanyID == companyId);

            if (existingTemplate == null)
                return;

            // Ana programı güncelle
            existingTemplate.ProgramName = templateUpdateDto.ProgramName;
            existingTemplate.Description = templateUpdateDto.Description;
            existingTemplate.ExperienceLevel = templateUpdateDto.ExperienceLevel;
            existingTemplate.TargetGoal = templateUpdateDto.TargetGoal;
            existingTemplate.IsActive = templateUpdateDto.IsActive;
            existingTemplate.UpdatedDate = System.DateTime.Now;

            // Mevcut günleri ve egzersizleri sil - SQL ile direkt silme
            var templateId = templateUpdateDto.WorkoutProgramTemplateID;

            // Önce egzersizleri sil
            context.Database.ExecuteSqlRaw(@"
                DELETE wpe FROM WorkoutProgramExercises wpe
                INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
                WHERE wpd.WorkoutProgramTemplateID = {0} AND wpd.CompanyID = {1}",
                templateId, companyId);

            // Sonra günleri sil
            context.Database.ExecuteSqlRaw(@"
                DELETE FROM WorkoutProgramDays
                WHERE WorkoutProgramTemplateID = {0} AND CompanyID = {1}",
                templateId, companyId);

            // Değişiklikleri kaydet
            context.SaveChanges();

            // Yeni günleri ekle
            foreach (var dayDto in templateUpdateDto.Days)
            {
                var day = new WorkoutProgramDay
                {
                    WorkoutProgramTemplateID = existingTemplate.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    DayNumber = dayDto.DayNumber,
                    DayName = dayDto.DayName,
                    IsRestDay = dayDto.IsRestDay,
                    CreationDate = System.DateTime.Now
                };

                context.WorkoutProgramDays.Add(day);
                context.SaveChanges(); // ID'yi almak için

                // Egzersizleri ekle (dinlenme günü değilse)
                if (!dayDto.IsRestDay && dayDto.Exercises != null)
                {
                    foreach (var exerciseDto in dayDto.Exercises)
                    {
                        var exercise = new WorkoutProgramExercise
                        {
                            WorkoutProgramDayID = day.WorkoutProgramDayID,
                            CompanyID = companyId,
                            ExerciseType = exerciseDto.ExerciseType,
                            ExerciseID = exerciseDto.ExerciseID,
                            OrderIndex = exerciseDto.OrderIndex,
                            Sets = exerciseDto.Sets,
                            Reps = exerciseDto.Reps,
                            RestTime = exerciseDto.RestTime,
                            Notes = exerciseDto.Notes,
                            CreationDate = System.DateTime.Now
                        };

                        context.WorkoutProgramExercises.Add(exercise);
                    }
                }
            }

            // Final save
            context.SaveChanges();
        }
    }
}
